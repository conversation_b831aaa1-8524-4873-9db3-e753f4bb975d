import { Dr<PERSON><PERSON><PERSON><PERSON> } from "@/modules/dashboard/components/data-table/drag-table";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { Input } from "@/shared/components/shadcn/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { ColumnDef } from "@tanstack/react-table";
import { Controller } from "react-hook-form";
import z from "zod";

export const inspectionFormModalColumnsSchema = z.object({
	id: z.string().optional(),
	campo: z.string().min(1, "Campo é obrigatório"),
	tipo_campo: z.string().min(1, "Tipo de campo é obrigatório"),
	medida: z.string().min(1, "Medida é obrigatória"),
	apelido: z.string().min(1, "Apelido é obrigatório"),
	obrigatorio: z.boolean().optional(),
	grupo: z.string().optional(),
	titulo_grupo: z.string().optional(),
	ordem: z.string().optional(),
	filtro_bi: z.boolean().optional(),
});

export const inspectionFormModalColumns: ColumnDef<z.infer<typeof inspectionFormModalColumnsSchema>>[] = [
	{
		id: "drag-form-inspection",
		header: () => null,
		cell: ({ row }) => <DragHandle id={Number(row.original.id) || 0} />,
	},
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
					onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-center">
				<Checkbox checked={row.getIsSelected()} onCheckedChange={value => row.toggleSelected(!!value)} aria-label="Select row" />
			</div>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "campo",
		header: "Campo",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller name={`campos.${idx}.campo`} control={control} render={({ field }) => <Input placeholder="Campo" {...field} />} />
			) : null;
		},
	},
	{
		accessorKey: "tipo_campo",
		header: "Tipo de campo",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller
					name={`campos.${idx}.tipo_campo`}
					control={control}
					render={({ field }) => (
						<Select onValueChange={field.onChange} onBlur={field.onBlur} value={field.value}>
							<SelectTrigger className="w-38 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
								<SelectValue placeholder="Tipo de campo" />
							</SelectTrigger>
							<SelectContent align="end">
								{row.original.tipo_campo && (
									<SelectItem value={row.original.tipo_campo} className="truncate">
										{row.original.tipo_campo}
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					)}
				/>
			) : null;
		},
	},
	{
		accessorKey: "medida",
		header: "Medida",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller name={`campos.${idx}.medida`} control={control} render={({ field }) => <Input placeholder="Medida" {...field} />} />
			) : null;
		},
	},
	{
		accessorKey: "apelido",
		header: "Apelido",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller name={`campos.${idx}.apelido`} control={control} render={({ field }) => <Input placeholder="Apelido" {...field} />} />
			) : null;
		},
	},
	{
		accessorKey: "grupo",
		header: "Grupo",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller name={`campos.${idx}.grupo`} control={control} render={({ field }) => <Input placeholder="Grupo" {...field} />} />
			) : null;
		},
	},
	{
		accessorKey: "titulo_grupo",
		header: "Título do grupo",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller
					name={`campos.${idx}.titulo_grupo`}
					control={control}
					render={({ field }) => <Input placeholder="Título do grupo" {...field} />}
				/>
			) : null;
		},
	},
	{
		accessorKey: "obrigatorio",
		header: "Obrigatório",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller
					name={`campos.${idx}.obrigatorio`}
					control={control}
					render={({ field }) => <Checkbox checked={field.value} onCheckedChange={field.onChange} onBlur={field.onBlur} />}
				/>
			) : null;
		},
	},
	{
		accessorKey: "filtro_bi",
		header: "Filtro BI",
		cell: ({ row, table }) => {
			const idx = row.index;
			const control = table.options.meta?.control;
			return control ? (
				<Controller
					name={`campos.${idx}.filtro_bi`}
					control={control}
					render={({ field }) => <Checkbox checked={field.value} onCheckedChange={field.onChange} onBlur={field.onBlur} />}
				/>
			) : null;
		},
	},
];
