import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { inspectionFormModalColumnsSchema } from "@/modules/inspection/components/register/tabs/forms/modal-colums";

// Schema principal do formulário
export const formModalSchema = z.object({
	titulo: z.string().min(1, "Título é obrigatório"),
	descricao: z.string().optional(),
	nomenclatura: z.string().min(1, "Nomenclatura é obrigatória"),
	revisao: z.union([z.string(), z.number()]).refine(val => String(val).length > 0, { message: "Revisão é obrigatória" }),
	elaborador: z.string().min(1, "Elaborador é obrigatório"),
	aprovador: z.string().min(1, "Aprovador é obrigatório"),
	campos: z.array(inspectionFormModalColumnsSchema),
});

export type IFormModalFormHook = z.infer<typeof formModalSchema>;

export interface IHandleFormModalFormHook {
	methods: ReturnType<typeof useForm<IFormModalFormHook>>;
	fields: ReturnType<typeof useFieldArray<IFormModalFormHook, "campos">>["fields"];
	append: ReturnType<typeof useFieldArray<IFormModalFormHook, "campos">>["append"];
	remove: ReturnType<typeof useFieldArray<IFormModalFormHook, "campos">>["remove"];
	update: ReturnType<typeof useFieldArray<IFormModalFormHook, "campos">>["update"];
	move: ReturnType<typeof useFieldArray<IFormModalFormHook, "campos">>["move"];
	onSubmit: ReturnType<typeof useForm<IFormModalFormHook>>["handleSubmit"];
}

export function useFormModalFormHook(initialValues?: Partial<IFormModalFormHook>): IHandleFormModalFormHook {
	const methods = useForm<IFormModalFormHook>({
		resolver: zodResolver(formModalSchema),
		defaultValues: {
			titulo: "",
			descricao: "",
			nomenclatura: "",
			revisao: "",
			elaborador: "",
			aprovador: "",
			campos: [],
			...initialValues,
		},
		mode: "onChange",
	});

	const { control } = methods;
	const { fields, append, remove, update, move } = useFieldArray({
		control,
		name: "campos",
	});

	return {
		methods,
		fields,
		append,
		remove,
		update,
		move,
		onSubmit: methods.handleSubmit,
	};
}
