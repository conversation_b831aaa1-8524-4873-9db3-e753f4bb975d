import { Button } from "@/shared/components/shadcn/button";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Row, flexRender } from "@tanstack/react-table";
import { GripVertical, Trash2 } from "lucide-react";
import React from "react";
import { inspectionFormModalColumnsSchema } from "./modal-colums";
import { z } from "zod";

interface IDraggableFieldRowProps {
	row: Row<z.infer<typeof inspectionFormModalColumnsSchema>>;
	index: number;
	onRemove: () => void;
}

export const DraggableFieldRow: React.FC<IDraggableFieldRowProps> = ({ row, index, onRemove }) => {
	const { transform, transition, setNodeRef, isDragging, attributes, listeners } = useSortable({
		id: index.toString(),
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	return (
		<TableRow
			ref={setNodeRef}
			style={style}
			data-state={row.getIsSelected() && "selected"}
			data-dragging={isDragging}
			className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80 data-[dragging=true]:shadow-lg data-[dragging=true]:bg-background data-[dragging=true]:border group hover:bg-muted/50 transition-all duration-200"
		>
			{/* Coluna de drag handle */}
			<TableCell className="w-12 p-2">
				<div className="flex items-center gap-1">
					<Button
						{...attributes}
						{...listeners}
						variant="ghost"
						size="icon"
						className="h-6 w-6 text-muted-foreground hover:bg-muted hover:text-foreground cursor-grab active:cursor-grabbing transition-colors opacity-0 group-hover:opacity-100"
						title="Arrastar para reordenar"
					>
						<GripVertical className="h-3 w-3" />
						<span className="sr-only">Arrastar para reordenar</span>
					</Button>
				</div>
			</TableCell>

			{/* Renderizar as outras células (exceto a primeira que é o drag handle e a última que são as ações) */}
			{row
				.getVisibleCells()
				.slice(1, -1)
				.map(cell => (
					<TableCell key={cell.id} className="relative">
						{flexRender(cell.column.columnDef.cell, cell.getContext())}
					</TableCell>
				))}

			{/* Coluna de ações */}
			<TableCell className="w-12 p-2">
				<div className="flex items-center justify-center">
					<Button
						variant="ghost"
						size="icon"
						onClick={onRemove}
						className="h-6 w-6 text-muted-foreground hover:text-destructive hover:bg-destructive/10 opacity-0 group-hover:opacity-100 transition-all duration-200"
						title="Remover campo"
					>
						<Trash2 className="h-3 w-3" />
						<span className="sr-only">Remover campo</span>
					</Button>
				</div>
			</TableCell>
		</TableRow>
	);
};
