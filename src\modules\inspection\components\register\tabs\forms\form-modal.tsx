import { IFormModalFormHook, useFormModalFormHook } from "@/modules/inspection/hooks/useFormModal";
import CustomInput from "@/shared/components/custom/custom-input";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { closestCenter, DndContext, KeyboardSensor, MouseSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { Label } from "@radix-ui/react-dropdown-menu";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import React from "react";
import { Control, Controller, FormProvider, UseFieldArrayRemove } from "react-hook-form";
import { inspectionFormModalColumns } from "./modal-colums";

// Estender o tipo TableMeta para incluir nossas propriedades customizadas
declare module "@tanstack/react-table" {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	interface TableMeta<TData> {
		control?: Control<IFormModalFormHook>;
		remove?: UseFieldArrayRemove;
	}
}

interface FormModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const FormModal: React.FC<FormModalProps> = ({ isOpen, onClose }) => {
	const sortableId = React.useId();
	const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));

	// Hook customizado para o formulário
	const { methods, fields: camposFields, append, remove, onSubmit } = useFormModalFormHook();

	const table = useReactTable({
		data: camposFields,
		columns: inspectionFormModalColumns,
		getCoreRowModel: getCoreRowModel(),
		meta: {
			control: methods.control,
			remove: remove,
		},
	});

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[2000px] !max-w-none" title="Cadastro de Formulário">
			<FormProvider {...methods}>
				<form
					onSubmit={onSubmit(() => {
						onClose();
					})}
					className="flex flex-col gap-4"
				>
					<Controller
						name="titulo"
						control={methods.control}
						render={({ field }) => (
							<CustomInput
								placeholder="Digite seu título"
								className="w-full"
								isRequired
								label="Título"
								name="titulo"
								type="text"
								value={field.value}
								onChange={field.onChange}
							/>
						)}
					/>
					<div className="flex flex-col items-start w-full gap-1.5 ">
						<Label className="text-sm text-text-primary">Descrição </Label>
						<Controller
							name="descricao"
							control={methods.control}
							render={({ field }) => <Textarea placeholder="Digite uma descrição para o formulário" {...field} />}
						/>
					</div>
					<div className="flex gap-3">
						<Controller
							name="nomenclatura"
							control={methods.control}
							render={({ field }) => (
								<CustomInput
									placeholder="Digite a nomenclatura "
									className="w-full"
									isRequired
									label="Nomenclatura"
									name="nomenclatura"
									type="text"
									value={field.value}
									onChange={field.onChange}
								/>
							)}
						/>
						<Controller
							name="revisao"
							control={methods.control}
							render={({ field }) => (
								<CustomInput
									placeholder="Digite a revisão"
									className="w-full"
									isRequired
									label="Revisão"
									name="revisao"
									type="number"
									value={field.value}
									onChange={field.onChange}
								/>
							)}
						/>
					</div>
					<div className="flex relative gap-3">
						<div className="flex flex-col items-start w-full gap-1.5 ">
							<Label className="text-sm text-text-primary">
								Elaborador <span className="text-red-500">*</span>
							</Label>
							<Controller
								name="elaborador"
								control={methods.control}
								render={({ field }) => (
									<Select value={field.value} onValueChange={field.onChange}>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um elaborador" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="Kevin Luan Damm">Kevin Luan Damm</SelectItem>
											<SelectItem value="Leonardo Lobas Rockenbach">Leonardo Lobas Rockenbach</SelectItem>
											<SelectItem value="Edi Mapepa">Edi Mapepa</SelectItem>
										</SelectContent>
									</Select>
								)}
							/>
						</div>
						<div className="flex flex-col items-start w-full gap-1.5 ">
							<Label className="text-sm text-text-primary">
								Aprovador <span className="text-red-500">*</span>
							</Label>
							<Controller
								name="aprovador"
								control={methods.control}
								render={({ field }) => (
									<Select value={field.value} onValueChange={field.onChange}>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um aprovador" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="Kevin Luan Damm">Kevin Luan Damm</SelectItem>
											<SelectItem value="Leonardo Lobas Rockenbach">Leonardo Lobas Rockenbach</SelectItem>
											<SelectItem value="Edi Mapepa">Edi Mapepa</SelectItem>
											<SelectItem value="ah blz">Jheisinho Marls</SelectItem>
										</SelectContent>
									</Select>
								)}
							/>
						</div>
					</div>

					{/* Botão para adicionar novo campo dinâmico */}
					<div className="flex justify-end mt-2">
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => append({ campo: "", tipo_campo: "", medida: "", apelido: "", obrigatorio: false })}
							className="flex items-center gap-2"
						>
							<Plus size={16} /> Adicionar campo
						</Button>
					</div>

					{/* Tabela dinâmica de campos */}
					<div className="flex flex-col gap-4 mt-2">
						<div className="overflow-hidden rounded-lg border">
							<DndContext
								collisionDetection={closestCenter}
								modifiers={[restrictToVerticalAxis]}
								onDragEnd={() => {}}
								sensors={sensors}
								id={sortableId}
							>
								<Table>
									<TableHeader className="bg-muted sticky top-0 z-10">
										{table.getHeaderGroups().map(headerGroup => (
											<TableRow key={headerGroup.id}>
												{headerGroup.headers.map(header => (
													<TableHead key={header.id} colSpan={header.colSpan}>
														{header.isPlaceholder
															? null
															: flexRender(header.column.columnDef.header, header.getContext())}
													</TableHead>
												))}
											</TableRow>
										))}
									</TableHeader>
									<TableBody>
										{table.getRowModel().rows?.length ? (
											table.getRowModel().rows.map(row => (
												<TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
													{row.getVisibleCells().map(cell => (
														<TableCell key={cell.id}>
															{flexRender(cell.column.columnDef.cell, cell.getContext())}
														</TableCell>
													))}
												</TableRow>
											))
										) : (
											<TableRow>
												<TableCell colSpan={inspectionFormModalColumns.length} className="h-24 text-center">
													Nenhum campo adicionado.
												</TableCell>
											</TableRow>
										)}
									</TableBody>
								</Table>
							</DndContext>
						</div>
					</div>

					<div className="flex justify-end gap-2 mt-4">
						<button type="button" className="btn btn-secondary" onClick={onClose}>
							Cancelar
						</button>
						<button type="submit" className="btn btn-primary">
							Salvar
						</button>
					</div>
				</form>
			</FormProvider>
		</Modal>
	);
};
