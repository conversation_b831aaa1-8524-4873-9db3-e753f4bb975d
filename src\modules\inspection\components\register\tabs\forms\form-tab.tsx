"use client";

import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Label } from "@/shared/components/shadcn/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, getPaginationRowModel, useReactTable } from "@tanstack/react-table";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import * as React from "react";
import { columns, IFormularioFormulariosTab } from "./columns";

const initialData: IFormularioFormulariosTab[] = [
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "<PERSON>",
		elaborador: "<PERSON>",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "João Silva",
		elaborador: "Maria Souza",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "João Silva",
		elaborador: "Maria Souza",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "João Silva",
		elaborador: "Maria Souza",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "João Silva",
		elaborador: "Maria Souza",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
	{
		titulo: "Formulário de Inspeção",
		texto: "Inspeção de equipamentos",
		nomenclatura: "FI-001",
		revisao: "1.0",
		aprovador: "João Silva",
		elaborador: "Maria Souza",
	},
	{
		titulo: "Checklist de Segurança",
		texto: "Verificação de EPIs",
		nomenclatura: "CL-002",
		revisao: "2.1",
		aprovador: "Ana Lima",
		elaborador: "Carlos Dias",
	},
];

export const FormulariosTab = ({ searchTerm }: { searchTerm?: string }) => {
	const [rowSelection, setRowSelection] = React.useState({});
	const [pagination, setPagination] = React.useState({
		pageIndex: 0,
		pageSize: 10,
	});
	const [data] = React.useState<IFormularioFormulariosTab[]>(initialData);

	const filteredData = React.useMemo(() => {
		if (!searchTerm) return data;
		return data.filter(
			item => item.titulo.toLowerCase().includes(searchTerm.toLowerCase()) || item.texto.toLowerCase().includes(searchTerm.toLowerCase())
		);
	}, [searchTerm, data]);

	const table = useReactTable({
		data: filteredData,
		columns,
		state: { rowSelection, pagination },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		onPaginationChange: setPagination,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	return (
		<div className="space-y-4">
			<div className="overflow-x-auto rounded-lg border bg-background">
				<Table>
					<TableHeader className="bg-muted sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} colSpan={header.colSpan} className="whitespace-nowrap text-xs font-semibold">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{table.getRowModel().rows.length ? (
							table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
									className={(idx % 2 === 0 ? "bg-muted/20" : "bg-background") + " transition-colors hover:bg-primary/10"}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={columns.length} className="h-24 text-center">
									Nenhum resultado encontrado.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			<div className="flex items-center justify-between px-4">
				<div className="text-muted-foreground hidden flex-1 text-sm lg:flex">
					{selectedCount} de {table.getFilteredRowModel().rows.length} linhas selecionadas.
				</div>
				<div className="flex w-full items-center gap-8 lg:w-fit">
					<div className="hidden items-center gap-2 lg:flex">
						<Label htmlFor="rows-per-page" className="text-sm font-medium">
							Linhas por página
						</Label>
						<Select
							value={`${table.getState().pagination.pageSize}`}
							onValueChange={value => {
								table.setPageSize(Number(value));
							}}
						>
							<SelectTrigger size="sm" className="w-20" id="rows-per-page">
								<SelectValue placeholder={table.getState().pagination.pageSize} />
							</SelectTrigger>
							<SelectContent side="top">
								{[10, 20, 30, 40, 50].map(pageSize => (
									<SelectItem key={pageSize} value={`${pageSize}`}>
										{pageSize}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="flex w-fit items-center justify-center text-sm font-medium">
						Página {table.getState().pagination.pageIndex + 1} de {table.getPageCount()}
					</div>
					<div className="ml-auto flex items-center gap-2 lg:ml-0">
						<Button
							variant="outline"
							className="hidden h-8 w-8 p-0 lg:flex"
							onClick={() => table.setPageIndex(0)}
							disabled={!table.getCanPreviousPage()}
						>
							<span className="sr-only">Ir para a primeira página</span>
							<ChevronsLeft />
						</Button>
						<Button
							variant="outline"
							className="size-8"
							size="icon"
							onClick={() => table.previousPage()}
							disabled={!table.getCanPreviousPage()}
						>
							<span className="sr-only">Ir para a página anterior</span>
							<ChevronLeft />
						</Button>
						<Button variant="outline" className="size-8" size="icon" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
							<span className="sr-only">Ir para a próxima página</span>
							<ChevronRight />
						</Button>
						<Button
							variant="outline"
							className="hidden size-8 lg:flex"
							size="icon"
							onClick={() => table.setPageIndex(table.getPageCount() - 1)}
							disabled={!table.getCanNextPage()}
						>
							<span className="sr-only">Ir para a última página</span>
							<ChevronsRight />
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
};
